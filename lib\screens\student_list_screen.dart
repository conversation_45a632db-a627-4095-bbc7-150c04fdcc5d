import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/student_provider.dart';

class StudentListScreen extends StatelessWidget {
  const StudentListScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final students = context.watch<StudentProvider>().students;
    return Scaffold(
      appBar: AppBar(title: const Text('Registered Students')),
      body: students.isEmpty
          ? const Center(child: Text('No students registered yet'))
          : ListView.builder(
              itemCount: students.length,
              itemBuilder: (context, index) {
                final student = students[index];
                return ListTile(
                  title: Text(student.name),
                  subtitle: Text('${student.level} \u2022 ${student.email}'),
                );
              },
            ),
    );
  }
}
