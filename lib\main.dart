import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/auth_provider.dart';
import 'providers/student_provider.dart';
import 'screens/login_screen.dart';
import 'screens/home_screen.dart';
import 'screens/registration_screen.dart';
import 'screens/schedule_screen.dart';
import 'screens/notifications_screen.dart';
import 'screens/add_schedule_screen.dart';
import 'providers/class_schedule_provider.dart';
import 'screens/student_list_screen.dart';
import 'theme.dart';


void main() {
  runApp(const SkatingApp());
}

class SkatingApp extends StatelessWidget {
  const SkatingApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => StudentProvider()),
        ChangeNotifierProvider(create: (_) => ClassScheduleProvider()),
      ],
      child: MaterialApp(
        title: 'Skating App',
        theme: AppTheme.light,
        initialRoute: '/login',
        routes: {
          '/login': (context) => const LoginScreen(),
          '/home': (context) => const HomeScreen(),
          '/register': (context) => const RegistrationScreen(),
          '/schedule': (context) => const ScheduleScreen(),
          '/add_schedule': (context) => const AddScheduleScreen(),
          '/notifications': (context) => const NotificationsScreen(),
          '/students': (context) => const StudentListScreen(),
        },
      ),
    );
  }
}
