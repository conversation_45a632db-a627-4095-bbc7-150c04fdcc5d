import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/class_schedule_provider.dart';
import '../widgets/gradient_background.dart';

class ScheduleScreen extends StatelessWidget {
  const ScheduleScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final sessions = context.watch<ClassScheduleProvider>().sessions;

    return Scaffold(
      appBar: AppBar(title: const Text('Scheduled Classes')),
      body: sessions.isEmpty
          ? const Center(child: Text('No classes scheduled'))
          : ListView.builder(
              itemCount: sessions.length,
              itemBuilder: (context, index) {
                final s = sessions[index];
                final date =
                    '${s.dateTime.month}/${s.dateTime.day}/${s.dateTime.year}';
                final time =
                    TimeOfDay.fromDateTime(s.dateTime).format(context);
                return Card(
                  margin: const EdgeInsets.all(8),
                  child: ListTile(
                    title: Text('${s.student.name} (${s.student.level})'),
                    subtitle: Text('$date \u2022 $time'),
                  ),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Navigator.pushNamed(context, '/add_schedule'),
        child: const Icon(Icons.add),
      body: GradientBackground(
        child: ListView.builder(
          itemCount: classes.length,
          itemBuilder: (context, index) {
            final cls = classes[index];
            return Card(
              margin: const EdgeInsets.all(8),
              child: ListTile(
                title: Text(cls['level']!),
                subtitle: Text('${cls['date']} \u2022 ${cls['time']}'),
              ),
            );
          },
        ),
      ),
    );
  }
}
