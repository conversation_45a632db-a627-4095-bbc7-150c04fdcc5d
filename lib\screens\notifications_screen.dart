import 'package:flutter/material.dart';
import '../widgets/gradient_background.dart';

class NotificationsScreen extends StatelessWidget {
  const NotificationsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Notifications')),
      body: GradientBackground(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
            Text(
              'Notifications Overview',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text(
              'The Skating App will keep coaches and students informed of important '
              'updates. Notifications will alert users about upcoming lessons, '
              'schedule changes and announcements.\n\n'
              'Push notifications will be delivered on supported devices while '
              'this screen provides a full history. Future releases will include '
              'options to mark items as read and customize alert preferences.',
            ),
            ],
          ),
        ),
      ),
    );
  }
}
