# Skating App

A Flutter application for skating coaches to manage students, schedules, and payments.

This repository currently contains the base project structure and navigation framework. The core features listed in the project specification will be implemented incrementally.

## Getting Started

Ensure you have Flutter installed. Clone this repository and run:

```bash
flutter pub get
flutter run
```


<<<<<<< codex/assign-new-class-timings
The app starts with a login screen. After logging in, you'll see a basic dashboard with links to register students and view scheduled classes. From the schedule page coaches can now assign class times to registered students.
=======

The app starts with a login screen. After logging in, you'll see a dashboard with a link to register students. The registration page now collects detailed information including name, email, age, phone number and skill level. Submitted students are stored locally for now.
=======
The app starts with a login screen. After logging in, you'll see a basic dashboard with a link to register students. This is just a foundation; more features will be added soon.
=======
The app starts with a login screen. After logging in, you'll see a dashboard with options to register students, view scheduled classes, and check notifications. You can now also view a list of all registered students from the dashboard. The registration page collects detailed information including name, email, age, phone number and skill level. Submitted students are stored locally for now.
>>>>>>> main

## Notifications Page

From the dashboard you can access the Notifications page which describes how upcoming alerts and announcements will appear in the app. Future versions will deliver push notifications and provide history in this section.
## Documentation

For instructions on integrating payments, see [docs/payment_integration.md](docs/payment_integration.md).

